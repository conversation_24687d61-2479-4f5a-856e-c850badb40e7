{"name": "weather-api-server", "version": "1.0.0", "description": "Express API server for Weather Agent integration", "main": "server.js", "type": "module", "scripts": {"start": "tsx server.ts", "dev": "tsx server.ts"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.0", "@modelcontextprotocol/sdk": "^1.12.0", "cors": "^2.8.5", "express": "^5.1.0", "zod": "^3.25.30"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.21", "tsx": "^4.19.4", "typescript": "^5.8.3"}}