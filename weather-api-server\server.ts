import express from 'express';
import cors from 'cors';
import { weatherAgent } from './weatherAgent.js';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors({
  origin: '*', // Allow all origins for development
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type']
}));

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Weather API Server is running' });
});

// Weather chat endpoint
app.post('/api/weather', async (req, res) => {
  try {
    const { message } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({ 
        error: 'Message is required and must be a string' 
      });
    }

    console.log('Received weather request:', message);

    // Use the weather agent to generate response
    const response = await weatherAgent.text({
      messages: [{ role: 'user', content: message }]
    });

    console.log('Weather agent response:', response);

    res.json({ 
      response: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error processing weather request:', error);
    res.status(500).json({ 
      error: 'Failed to process weather request',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🌤️  Weather API Server running on http://localhost:${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Weather endpoint: http://localhost:${PORT}/api/weather`);
});

export default app;
