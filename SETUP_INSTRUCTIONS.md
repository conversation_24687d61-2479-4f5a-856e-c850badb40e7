# Weather Agent React Native Integration Setup

## Overview
This setup connects your Mastra Weather Agent with your React Native mobile app through a separate Express API server.

## Prerequisites
- Node.js (>=20.9.0)
- All dependencies installed in all three directories

## Setup Steps

### 1. Find Your Computer's IP Address
Before starting, you need to find your computer's local IP address:

**Windows:**
```bash
ipconfig
```
Look for "IPv4 Address" under your active network connection (usually starts with 192.168.x.x)

**macOS/Linux:**
```bash
ifconfig
```
Look for your network interface (usually en0 or wlan0) and find the inet address.

### 2. Update API Configuration
Edit `a0-project/screens/HomeScreen.tsx` and replace the IP address in line 24:
```typescript
const API_BASE_URL = 'http://YOUR_IP_ADDRESS:3001';
```
Replace `YOUR_IP_ADDRESS` with your actual IP address from step 1.

### 3. Start the Services
You need to run three services in separate terminal windows:

**Terminal 1 - Mastra Service:**
```bash
npm run dev
```

**Terminal 2 - Express API Server:**
```bash
npm run server
```

**Terminal 3 - React Native App:**
```bash
cd a0-project
npm start
```

## Testing the Integration

1. Once all services are running, open your React Native app on a device/emulator
2. Try asking weather questions like:
   - "What's the weather like in New York?"
   - "Will it rain tomorrow in London?"
   - "What's the temperature in Tokyo?"

## Troubleshooting

### Common Issues:

1. **"Network request failed"**
   - Check that the Express server is running on port 3001
   - Verify the IP address in HomeScreen.tsx is correct
   - Make sure your device/emulator is on the same network

2. **"Failed to process weather request"**
   - Check the Express server logs for errors
   - Ensure Mastra service is running properly
   - Verify MCP weather service is accessible

3. **Agent not responding**
   - Check that the Weather Agent is properly initialized
   - Verify MCP client configuration in `src/mastra/mcp/weatherMcp.ts`
   - Check console logs for any initialization errors

### Logs to Check:
- Express server console for API request logs
- React Native Metro bundler for client-side errors
- Mastra dev console for agent-related issues

## Architecture

```
React Native App (Port: Expo Dev Server)
    ↓ HTTP POST
Express API Server (Port: 3001)
    ↓ Function Call
Mastra Weather Agent
    ↓ MCP Protocol
Weather MCP Service (Smithery)
```

## Next Steps

Once everything is working:
1. Test with various weather queries
2. Monitor performance and error handling
3. Consider adding authentication if needed
4. Implement additional features like location services
